// Main Game Initialization and Management

import { gameState } from './gameState.js';
import { initializeAudio, loadMusicFile, toggleMusic, toggleSoundEffects } from './audio.js';
import { initializeMiningDisplay, toggleAutoMining } from './mining.js';
import { initializeInventory, sellAllMinerals } from './inventory.js';
import { buyDrillEnhancement, buyVeinChanceBoost, buyInventoryUpgrade, buyAutoSell, prestigeReset } from './shop.js';
import { initializeUI, updateUI, toggleShop, toggleSettings, toggleStats, toggleEncyclopedia, toggleFoundMinerals, toggleEvents, toggleStorage, closeAllPanels, updateScreenSize, toggleFullscreen } from './ui.js';
import { saveGame, loadGame, resetGame, startAutoSave, initializeSaveOnUnload } from './saveLoad.js';
import { initializeEventSystem, skipEvent, clearEventHistory } from './events.js';
import { initializeStorage, moveArtifactToStorage, moveArtifactToInventory, sellAllArtifactsFromStorage } from './storage.js';
import { showNotification } from './utils.js';

// Global game instance for browser console access
window.game = {
  state: gameState,
  save: saveGame,
  load: loadGame,
  reset: resetGame
};

// Main initialization function
function initializeGame() {
  console.log('🎮 Initializing Idle Miner...');

  try {
    // Initialize core systems
    initializeInventory();
    initializeStorage();
    initializeAudio();
    initializeMiningDisplay();
    initializeEventSystem();
    initializeUI();

    // Initialize save system
    initializeSaveOnUnload();
    startAutoSave(1); // Auto-save every minute

    // Try to load existing save
    const loadSuccess = loadGame();
    if (!loadSuccess) {
      console.log('No save file found, starting new game');
      updateUI();
    }

    console.log('✅ Idle Miner initialized successfully!');
    showNotification('Welcome to Idle Miner!', 'success');

  } catch (error) {
    console.error('❌ Failed to initialize game:', error);
    showNotification('Failed to initialize game!', 'error');
  }
}

// Global functions for HTML onclick handlers
window.gameActions = {
  // Mining
  toggleAutoMining,

  // Inventory
  sellAllMinerals,

  // Shop
  buyDrillEnhancement,
  buyVeinChanceBoost,
  buyInventoryUpgrade,
  buyAutoSell,
  prestigeReset,

  // UI
  toggleShop,
  toggleSettings,
  toggleStats,
  toggleEncyclopedia,
  toggleFoundMinerals,
  toggleEvents,
  toggleStorage,
  closeAllPanels,
  updateScreenSize,
  toggleFullscreen,

  // Audio
  loadMusicFile,
  toggleMusic,
  toggleSoundEffects,

  // Save/Load
  saveGame,
  loadGame,
  resetGame,

  // Events
  skipEvent,
  clearEventHistory,

  // Storage
  moveArtifactToStorage,
  moveArtifactToInventory,
  sellAllArtifactsFromStorage
};

// Make functions available globally for HTML onclick handlers
Object.assign(window, window.gameActions);

// Game loop for continuous updates
function gameLoop() {
  try {
    // Update UI periodically
    updateUI();

    // Check for achievements, events, etc.
    // (These are handled by their respective systems)

  } catch (error) {
    console.error('Error in game loop:', error);
  }
}

// Start game loop (runs every 5 seconds for UI updates)
setInterval(gameLoop, 5000);

// Performance monitoring
let lastFrameTime = performance.now();
let frameCount = 0;

function monitorPerformance() {
  frameCount++;
  const currentTime = performance.now();

  if (currentTime - lastFrameTime >= 10000) { // Every 10 seconds
    const fps = frameCount / ((currentTime - lastFrameTime) / 1000);
    console.log(`Performance: ${fps.toFixed(1)} updates/sec`);
    frameCount = 0;
    lastFrameTime = currentTime;
  }
}

// Add performance monitoring to game loop
setInterval(monitorPerformance, 100);

// Error handling for uncaught errors
window.addEventListener('error', (event) => {
  console.error('Uncaught error:', event.error);
  showNotification('An unexpected error occurred. Check console for details.', 'error');
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  showNotification('An unexpected error occurred. Check console for details.', 'error');
});

// Keyboard shortcuts
document.addEventListener('keydown', (event) => {
  // Only handle shortcuts if no input is focused
  if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
    return;
  }

  switch (event.key.toLowerCase()) {
    case ' ': // Spacebar - toggle mining
      event.preventDefault();
      toggleAutoMining();
      break;
    case 's': // S - sell all minerals
      if (event.ctrlKey) {
        event.preventDefault();
        saveGame();
      } else {
        sellAllMinerals();
      }
      break;
    case 'l': // L - load game
      if (event.ctrlKey) {
        event.preventDefault();
        loadGame();
      }
      break;
    case 'escape': // Escape - close panels
      closeAllPanels();
      break;
    case '1': // Number keys for panels
      toggleShop();
      break;
    case '2':
      toggleStats();
      break;
    case '3':
      toggleEncyclopedia();
      break;
    case '4':
      toggleFoundMinerals();
      break;
    case '5':
      toggleStorage();
      break;
    case '6':
      toggleEvents();
      break;
    case '7':
      toggleSettings();
      break;
    case 'm': // M - toggle music
      toggleMusic();
      break;
    case 'f': // F - toggle fullscreen
      toggleFullscreen();
      break;
  }
});

// Touch/mobile support
let touchStartY = 0;
let touchEndY = 0;

document.addEventListener('touchstart', (event) => {
  touchStartY = event.changedTouches[0].screenY;
});

document.addEventListener('touchend', (event) => {
  touchEndY = event.changedTouches[0].screenY;
  handleSwipe();
});

function handleSwipe() {
  const swipeThreshold = 50;
  const diff = touchStartY - touchEndY;

  if (Math.abs(diff) > swipeThreshold) {
    if (diff > 0) {
      // Swipe up - could open a panel
      console.log('Swipe up detected');
    } else {
      // Swipe down - could close panels
      closeAllPanels();
    }
  }
}

// Visibility change handling (for mobile/tab switching)
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    console.log('Game hidden - pausing non-essential updates');
    // Could pause animations, reduce update frequency, etc.
  } else {
    console.log('Game visible - resuming normal operation');
    updateUI(); // Refresh UI when returning to game
  }
});

// Window resize handling
window.addEventListener('resize', () => {
  // Handle responsive design adjustments if needed
  console.log('Window resized');
});

// Initialize the game when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeGame);
} else {
  // DOM is already loaded
  initializeGame();
}

// Export for debugging
window.debugGame = {
  gameState,
  showNotification,
  updateUI,
  initializeGame
};

console.log('🎮 Idle Miner main.js loaded');
console.log('💡 Available keyboard shortcuts:');
console.log('  Space: Toggle mining');
console.log('  S: Sell all minerals');
console.log('  Ctrl+S: Save game');
console.log('  Ctrl+L: Load game');
console.log('  Escape: Close panels');
console.log('  1-7: Open panels (Shop, Stats, Encyclopedia, Found Minerals, Storage, Events, Settings)');
console.log('  M: Toggle music');
console.log('  F: Toggle fullscreen');
console.log('💻 Debug commands available in window.game and window.debugGame');
