// Artifact system for Idle Miner
// Handles artifact definitions, spawning, and management

// Artifact definitions with name, description, rarity, and sell price
export const ARTIFACTS = [
  {
    id: 'ancient_pickaxe',
    name: 'Ancient Pickaxe',
    description: 'A weathered mining tool from a bygone era. Its handle bears the marks of countless excavations.',
    rarity: 'Common',
    sellPrice: 500,
    icon: '⛏️'
  },
  {
    id: 'crystal_compass',
    name: 'Crystal Compass',
    description: 'A mystical navigation device that points toward mineral veins. The crystal glows faintly with inner light.',
    rarity: 'Uncommon',
    sellPrice: 1200,
    icon: '🧭'
  },
  {
    id: 'dwarven_lantern',
    name: 'Dwarven Lantern',
    description: 'An ornate lantern with eternal flame. Crafted by master dwarven smiths in the deep mines.',
    rarity: 'Rare',
    sellPrice: 2500,
    icon: '🏮'
  },
  {
    id: 'void_stone_tablet',
    name: 'Void Stone Tablet',
    description: 'A mysterious tablet inscribed with ancient mining techniques. The text seems to shift when not observed directly.',
    rarity: 'Epic',
    sellPrice: 5000,
    icon: '📜'
  },
  {
    id: 'starfall_meteorite',
    name: 'Starfall Meteorite',
    description: 'A fragment of a fallen star, pulsing with cosmic energy. It radiates warmth and seems to whisper secrets of the universe.',
    rarity: 'Legendary',
    sellPrice: 10000,
    icon: '☄️'
  }
];

// Artifact spawn rates (2% base chance as requested)
export const ARTIFACT_SPAWN_RATES = {
  base: 0.02, // 2% base chance
  veinMultiplier: 2, // 2x chance during vein discovery
  tierBonus: 0.001 // +0.1% per tier
};

// Rarity weights for artifact selection
export const RARITY_WEIGHTS = {
  'Common': 50,
  'Uncommon': 30,
  'Rare': 15,
  'Epic': 4,
  'Legendary': 1
};

// Get artifact spawn chance based on current conditions
export function getArtifactSpawnChance(currentTier, isVeinDiscovery = false) {
  let chance = ARTIFACT_SPAWN_RATES.base;
  
  // Add tier bonus
  chance += currentTier * ARTIFACT_SPAWN_RATES.tierBonus;
  
  // Apply vein multiplier if applicable
  if (isVeinDiscovery) {
    chance *= ARTIFACT_SPAWN_RATES.veinMultiplier;
  }
  
  return Math.min(chance, 0.1); // Cap at 10%
}

// Select a random artifact based on rarity weights
export function selectRandomArtifact() {
  // Create weighted array
  const weightedArtifacts = [];
  
  ARTIFACTS.forEach(artifact => {
    const weight = RARITY_WEIGHTS[artifact.rarity] || 1;
    for (let i = 0; i < weight; i++) {
      weightedArtifacts.push(artifact);
    }
  });
  
  // Select random artifact
  const randomIndex = Math.floor(Math.random() * weightedArtifacts.length);
  return weightedArtifacts[randomIndex];
}

// Check if an artifact should spawn during mining
export function shouldSpawnArtifact(currentTier, isVeinDiscovery = false) {
  const spawnChance = getArtifactSpawnChance(currentTier, isVeinDiscovery);
  return Math.random() < spawnChance;
}

// Get artifact by ID
export function getArtifactById(id) {
  return ARTIFACTS.find(artifact => artifact.id === id);
}

// Get all artifacts
export function getAllArtifacts() {
  return [...ARTIFACTS];
}

// Format artifact for display
export function formatArtifactDisplay(artifact, quantity = 1) {
  const quantityText = quantity > 1 ? ` x${quantity}` : '';
  return `${artifact.icon} ${artifact.name}${quantityText} (${artifact.rarity}) - ${artifact.sellPrice} coins`;
}

// Calculate total value of artifacts
export function calculateArtifactValue(artifactCounts) {
  let totalValue = 0;
  
  Object.entries(artifactCounts).forEach(([artifactId, count]) => {
    const artifact = getArtifactById(artifactId);
    if (artifact && count > 0) {
      totalValue += artifact.sellPrice * count;
    }
  });
  
  return totalValue;
}
