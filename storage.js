// Storage Room system for Idle Miner
// Handles artifact storage, viewing, and selling

import { getArtifactById, formatArtifactDisplay, calculateArtifactValue } from './artifacts.js';

// Storage state
let storageArtifacts = {}; // { artifactId: count }

// Initialize storage system
export function initializeStorage() {
  storageArtifacts = {};
  updateStorageDisplay();
}

// Add artifact to storage
export function addArtifactToStorage(artifactId, quantity = 1) {
  if (!storageArtifacts[artifactId]) {
    storageArtifacts[artifactId] = 0;
  }
  storageArtifacts[artifactId] += quantity;
  updateStorageDisplay();
  
  const artifact = getArtifactById(artifactId);
  if (artifact) {
    console.log(`Added ${quantity}x ${artifact.name} to storage`);
    return true;
  }
  return false;
}

// Remove artifact from storage
export function removeArtifactFromStorage(artifactId, quantity = 1) {
  if (!storageArtifacts[artifactId] || storageArtifacts[artifactId] < quantity) {
    return false;
  }
  
  storageArtifacts[artifactId] -= quantity;
  if (storageArtifacts[artifactId] <= 0) {
    delete storageArtifacts[artifactId];
  }
  
  updateStorageDisplay();
  return true;
}

// Get artifact count in storage
export function getStorageArtifactCount(artifactId) {
  return storageArtifacts[artifactId] || 0;
}

// Get all stored artifacts
export function getStoredArtifacts() {
  return { ...storageArtifacts };
}

// Check if storage has any artifacts
export function hasStoredArtifacts() {
  return Object.keys(storageArtifacts).length > 0;
}

// Sell single artifact from storage
export function sellArtifactFromStorage(artifactId) {
  const artifact = getArtifactById(artifactId);
  if (!artifact || !removeArtifactFromStorage(artifactId, 1)) {
    return false;
  }
  
  // Add coins to player (assuming global coins variable)
  if (typeof window !== 'undefined' && window.coins !== undefined) {
    window.coins += artifact.sellPrice;
    updateDisplay();
  }
  
  // Play sell sound if available
  if (typeof window !== 'undefined' && window.sellSound) {
    window.sellSound.play().catch(e => console.log('Could not play sell sound'));
  }
  
  showNotification(`Sold ${artifact.name} for ${artifact.sellPrice} coins!`);
  return true;
}

// Sell all artifacts from storage
export function sellAllArtifactsFromStorage() {
  if (!hasStoredArtifacts()) {
    showNotification('No artifacts in storage to sell!');
    return 0;
  }
  
  let totalValue = 0;
  let totalSold = 0;
  
  Object.entries(storageArtifacts).forEach(([artifactId, count]) => {
    const artifact = getArtifactById(artifactId);
    if (artifact && count > 0) {
      totalValue += artifact.sellPrice * count;
      totalSold += count;
    }
  });
  
  // Clear storage
  storageArtifacts = {};
  updateStorageDisplay();
  
  // Add coins to player
  if (typeof window !== 'undefined' && window.coins !== undefined) {
    window.coins += totalValue;
    updateDisplay();
  }
  
  // Play sell sound if available
  if (typeof window !== 'undefined' && window.sellSound) {
    window.sellSound.play().catch(e => console.log('Could not play sell sound'));
  }
  
  showNotification(`Sold ${totalSold} artifacts for ${totalValue} coins!`);
  return totalValue;
}

// Update storage display
export function updateStorageDisplay() {
  const storageList = document.getElementById('storage-list');
  if (!storageList) return;
  
  if (!hasStoredArtifacts()) {
    storageList.innerHTML = '<p style="color: #888; font-style: italic;">No artifacts stored</p>';
    return;
  }
  
  let html = '';
  Object.entries(storageArtifacts).forEach(([artifactId, count]) => {
    const artifact = getArtifactById(artifactId);
    if (artifact && count > 0) {
      html += `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px;">
          <div>
            <strong>${formatArtifactDisplay(artifact, count)}</strong>
            <br>
            <small style="color: #ccc;">${artifact.description}</small>
          </div>
          <button onclick="sellArtifactFromStorage('${artifactId}')" 
                  style="background: #4CAF50; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; margin-left: 10px;">
            Sell (${artifact.sellPrice})
          </button>
        </div>
      `;
    }
  });
  
  // Add total value display
  const totalValue = calculateArtifactValue(storageArtifacts);
  html += `
    <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #555;">
      <strong>Total Storage Value: ${totalValue} coins</strong>
    </div>
  `;
  
  storageList.innerHTML = html;
}

// Toggle storage panel visibility
export function toggleStorage() {
  const panel = document.getElementById('storage-panel');
  const button = document.getElementById('storage-btn');
  
  if (!panel || !button) return;
  
  const isHidden = panel.getAttribute('aria-hidden') === 'true';
  
  if (isHidden) {
    // Close all other panels first
    if (typeof window.closeAllPanels === 'function') {
      window.closeAllPanels();
    }
    
    // Show storage panel
    panel.setAttribute('aria-hidden', 'false');
    panel.style.display = 'block';
    button.setAttribute('aria-pressed', 'true');
    
    // Update display
    updateStorageDisplay();
  } else {
    // Hide storage panel
    panel.setAttribute('aria-hidden', 'true');
    panel.style.display = 'none';
    button.setAttribute('aria-pressed', 'false');
  }
}

// Save storage data
export function saveStorageData() {
  return {
    storageArtifacts: { ...storageArtifacts }
  };
}

// Load storage data
export function loadStorageData(data) {
  if (data && data.storageArtifacts) {
    storageArtifacts = { ...data.storageArtifacts };
    updateStorageDisplay();
  }
}

// Utility function to show notifications (fallback if not available globally)
function showNotification(message) {
  if (typeof window !== 'undefined' && typeof window.showNotification === 'function') {
    window.showNotification(message);
  } else {
    console.log('Notification:', message);
  }
}

// Utility function to update display (fallback if not available globally)
function updateDisplay() {
  if (typeof window !== 'undefined' && typeof window.updateDisplay === 'function') {
    window.updateDisplay();
  }
}

// Make functions available globally for HTML onclick handlers
if (typeof window !== 'undefined') {
  window.sellArtifactFromStorage = sellArtifactFromStorage;
  window.sellAllArtifactsFromStorage = sellAllArtifactsFromStorage;
  window.toggleStorage = toggleStorage;
}
